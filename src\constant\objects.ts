export const ENUM_DATA = {
  ACTIVE_LOAN: 'Active',
};

export const UserStage = {
  phone_verification: 1,
  basic_details: 2,
  selfie: 3,
  not_eligible: 4,
  pin: 5,
  aadhaar: 6,
  employement: 7,
  banking: 8,
  loan_accept: 10,
  pan: 12,
  final_verification: 13,
  mandate: 14,
  esign: 15,
  disbursement: 16,
  repayment: 17,
  defaulter: 18,
  re_apply: 19,
  no_route: 20,
  express_reapply: 21,
  on_hold: 23,
};

export const EXPERIAN_GOOD_COMPANIES = [
  'PLEBGRAPH FINANCE PVT LTD',
  'EARLYSALARY SERVICES PVT LTD',
  'FINKURVE FINANCIAL SERVICES LTD',
  'CITRA FINANCIALS PRIVATE LIMITED',
  'NIYOGIN FINTECH LIMITED',
  'VAIBHAV VYAPAAR PVT LTD',
  'MAS FINANCIAL SERVICES LTD',
  'VIVRITI CAPITAL LIMITED',
  'HDB FINANCIAL SERVICES LIMITED',
  'UNIFINZ CAPITAL INDIA LIMITED',
  'RESPO FINANCIAL CAPITAL PRIVATE LIMITED',
  'INCRED FINANCIAL SERVICES LIMITED',
  'RULOANS FINANCIAL SERVICES PRIVATE LIMITED',
  'VIVIFI INDIA FINANCE (P) LTD',
  'FDPL FINANCE PRIVATE LIMITED',
  'TAPSTART CAPITAL PVT LTD',
  'GIRDHAR FINLEASE PVT LTD',
  'ICICI BANK',
  'AKARA CAPITAL ADVISORS PRIVATE LIMITED',
  'UPMOVE CAPITAL PVT LTD',
  'CAPRI GLOBAL CAPITAL LIMITED',
  'KASAR CREDIT AND CAPITAL PRIVATE LIMITED',
  'INNOFIN SOLUTIONS PVT LTD',
  'ADITYA BIRLA FINANCE LTD',
  'HDFC BANK LTD',
  'FEDERAL BANK',
  'BABA LEASE AND INVESTMENT PVT LTD',
  'MAHASHAKTI FINANCIERS LIMITED',
  'KISETSU SAISON FINANCE (INDIA) PRIVATE LIMITED',
  'ARNOLD HOLDINGS LTD',
  'SOLOMON CAPITAL PRIVATE LIMITED',
  'SMFG INDIA CREDIT COMPANY LIMITED',
  'R K BANSAL FINANCE PRIVATE LIMITED',
  'POONAWALLA FINCORP LIMITED',
  'OXYZO FINANCIAL SERVICES PVT LTD',
  'SALORA CAPITAL LIMITED',
  'NAVI FINSERV LIMITED',
  'ZED LEAFIN PVT.LTD',
  'PAYU FINANCE INDIA PVT LTD',
  'SI CREVA CAPITAL',
  'ACHIIEVERS FINANCE INDIA PRIVATE LIMITED',
  'BHAWANA SECURITIES AND FINANCIAL SERVICES LTD',
  'STATE BANK OF INDIA',
  'FINC FRIENDS PRIVATE LIMITED',
  'GRAND TOTAL',
  'SAYYAM INVESTMETS PVT LTD',
  'KOTAK MAHINDRA BANK LIMITED',
  'NOT_DEFINED',
  'AXIS BANK',
  'SAMPATI SECURITIES LIMITED',
  'NORTHERN ARC CAPITAL LIMITED',
  'TRUE CREDITS PRIVATE LIMITED',
  'MPOKKET FINANCIAL SERVICES PVT LTD',
  'KRAZYBEE SERVICES PRIVATE LIMITED',
  'MUTHOOT FINANCE LIMITED',
  'IDFC FIRST BANK LIMITED',
  'BAJAJ FINANCE LIMITED',
  'MAHAVIRA FINLEASE LIMITED',
  'BRANCH INTERNATIONAL FINANCIAL SERVICES PVT LTD',
];

export const DATA_CODES_CLICKHOUSE_TABLE = {
  emi_details: 'emi_details',
  experian_soft_eligible: 'experian_soft_eligible',
  payment_details: 'payment_details',
};

export const NUMBERS = {
  THREE_HOURS_IN_SECONDS: 3 * 60 * 60,
};

export const GRAPH_TYPE = {
  0: 'Bar',
  1: 'Pie',
  2: 'Box',
  3: 'Line',
};
export const CLOUD_FOLDER = {
  reports: 'REPORTS',
  default: 'DEFAULT',
};

export const kMimeTypes = {
  json: 'application/json',
  formdata: 'multipart/form-data',
  csv: 'text/csv',
  doc: 'application/msword',
  docs: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  html: 'text/html',
  css: 'text/css',
  jpg: 'image/jpeg',
  JPG: 'image/jpeg',
  jpeg: 'image/jpeg',
  JPEG: 'image/jpeg',
  pdf: 'application/pdf',
  PDF: 'application/pdf',
  png: 'image/png',
  PNG: 'image/png',
  gif: 'image/gif',
  GIF: 'image/gif',
  svg: 'image/svg+xml',
  SVG: 'image/svg+xml',
  txt: 'text/plain',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
};

export const kFonts = {
  big: {
    font: {
      size: 16,
    },
    alignment: {
      wrapText: true,
    },
  },
  small: {
    font: {
      size: 12,
    },
    alignment: {
      wrapText: true,
    },
  },
};

export const kEMIEnums = {
  ONTIME: 'ON_TIME',
  PREPAID: 'PRE_PAID',
  DEFAULTER: 'DEFAULTER',
  DELAYED: 'DELAYED',
  UPCOMING: 'UPCOMING',
};

export const kServerStr = {
  nestJsInit: 'Attempting to start the NestJs server...',
  runningPort: 'Server is running on port -> ',
};

export const kRouteEnums = { SELECT_LOAN_AMOUNT: 'selectLoanAmountRoute' };
